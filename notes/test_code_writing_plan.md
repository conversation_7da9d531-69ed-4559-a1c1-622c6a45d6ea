# Dauntless Project Test Code Writing Plan

## Overview

This document outlines a comprehensive testing strategy for the Dauntless project, covering the main Flutter application, the common package, and the dauntless_server package. The project uses a multi-package architecture with shared models and a Dart Frog server backend.

## Current Testing State

### Existing Test Infrastructure
- **Main App**: Basic Flutter test setup with `flutter_test` and `test` packages
- **Server**: Dart Frog with `test`, `mocktail`, and `very_good_analysis` packages
- **Common**: Basic Dart package with `test` package

### Current Test Coverage
- Main app: Minimal (placeholder widget test)
- Server: Basic route testing (index route only)
- Common: No tests currently

## Testing Strategy by Package

### 1. Common Package (`packages/common`)

**Priority: HIGH** - Foundation for all other components

#### Models Testing
- **Game Model** (`lib/models/game.dart`)
  - JSON serialization/deserialization
  - Factory constructors
  - Validation of required fields

- **GameMatch Model** (`lib/models/game_match.dart`)
  - Complex model with multiple relationships
  - Status transitions (open → in_progress → completed)
  - Player slot management
  - Turn tracking
  - JSON serialization with custom converters

- **Player Models** (`lib/models/player.dart`, `lib/models/player_slot.dart`)
  - Player creation and validation
  - Player slot assignment and management
  - Player type handling

- **Turn Model** (`lib/models/turn.dart`)
  - Turn data structure validation
  - Turn sequence management

#### Converters Testing
- **Custom JSON Converters** (`lib/models/converters/`)
  - ID generation converters
  - Timestamp converters
  - Enum converters

#### Test Files to Create:
```
packages/common/test/
├── models/
│   ├── game_test.dart
│   ├── game_match_test.dart
│   ├── player_test.dart
│   ├── player_slot_test.dart
│   ├── turn_test.dart
│   └── converters/
│       ├── id_converter_test.dart
│       └── timestamp_converter_test.dart
└── test_helpers/
    └── model_factories.dart
```

### 2. Dauntless Server (`packages/dauntless_server`)

**Priority: HIGH** - Critical backend functionality

#### API Routes Testing
- **Match Management Routes**
  - `GET /matches` - List open matches
  - `GET /matches/{id}` - Get specific match
  - `POST /matches` - Create new match
  - `POST /matches/{id}/join` - Join match
  - `POST /matches/{id}/leave` - Leave match

- **Game Routes**
  - Game type endpoints
  - Game configuration endpoints

- **Turn Management**
  - `POST /api/turns` - Submit turn data

#### WebSocket Testing
- **WebSocketManager** (`lib/websockets/websocket_manager.dart`)
  - Client connection/disconnection
  - Match subscription/unsubscription
  - Message broadcasting
  - Error handling and reconnection

#### Integration Testing
- **End-to-End Scenarios**
  - Complete match lifecycle
  - Multi-client interactions
  - WebSocket + HTTP API integration

#### Test Files to Create:
```
packages/dauntless_server/test/
├── routes/
│   ├── matches/
│   │   ├── index_test.dart
│   │   ├── match_detail_test.dart
│   │   ├── join_match_test.dart
│   │   └── leave_match_test.dart
│   ├── games/
│   │   └── games_test.dart
│   └── api/
│       └── turns_test.dart
├── websockets/
│   └── websocket_manager_test.dart
├── integration/
│   ├── match_lifecycle_test.dart
│   └── websocket_integration_test.dart
└── test_helpers/
    ├── test_server.dart
    ├── mock_websocket.dart
    └── test_data.dart
```

### 3. Main Flutter Application

**Priority: MEDIUM** - UI and business logic

#### Repository Testing
- **ServerRepository** (`lib/repositories/server_repository.dart`)
  - API client interactions
  - Error handling
  - Data transformation

- **Match Repository** (`lib/repositories/match_repository.dart`)
  - Local match management
  - State synchronization

#### Use Cases Testing
- **Match Management Use Cases**
  - Create match
  - Join/leave match
  - Submit turns

- **Game Logic Use Cases**
  - Game state management
  - Turn processing
  - Player actions

#### BLoC Testing
- **Command Center BLoC** (`lib/ui/liberator/blocs/command_center/`)
  - State transitions
  - Event handling
  - Side effects

- **Theme BLoC** (`lib/ui/blocs/theme/`)
  - Theme switching
  - Persistence

#### Widget Testing
- **Screen Testing**
  - Command center screen
  - Match selection screens
  - Game UI components

- **Component Testing**
  - Custom widgets
  - Form validation
  - User interactions

#### Test Files to Create:
```
test/
├── repositories/
│   ├── server_repository_test.dart
│   ├── match_repository_test.dart
│   └── players_repository_test.dart
├── use_cases/
│   ├── match_management_use_case_test.dart
│   ├── game_logic_use_case_test.dart
│   └── logging_use_case_test.dart
├── blocs/
│   ├── command_center/
│   │   └── command_center_bloc_test.dart
│   └── theme/
│       └── theme_bloc_test.dart
├── ui/
│   ├── screens/
│   │   ├── command_center_screen_test.dart
│   │   └── match_selection_screen_test.dart
│   └── widgets/
│       ├── match_card_test.dart
│       └── player_list_test.dart
├── frameworks/
│   ├── game_match/
│   │   └── game_match_manager_test.dart
│   └── user/
│       └── user_manager_test.dart
└── test_helpers/
    ├── mock_repositories.dart
    ├── test_blocs.dart
    └── widget_test_helpers.dart
```

## Testing Tools and Frameworks

### Dependencies to Add/Verify

#### Main App (`pubspec.yaml`)
```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  test: any
  mocktail: ^1.0.0  # Add if not present
  bloc_test: ^9.1.0  # Add for BLoC testing
  golden_toolkit: ^0.15.0  # Add for golden tests
```

#### Server (`packages/dauntless_server/pubspec.yaml`)
```yaml
dev_dependencies:
  test: 1.25.15
  mocktail: 1.0.4
  shelf: 1.4.2
  very_good_analysis: 7.0.0
  # Already configured correctly
```

#### Common (`packages/common/pubspec.yaml`)
```yaml
dev_dependencies:
  test: ^1.24.0
  mocktail: ^1.0.0  # Add for mocking
  # Already has test package
```

## Implementation Phases

### Phase 1: Foundation (Week 1)
1. **Common Package Models**
   - Set up test structure
   - Create model factories
   - Test core models (Game, GameMatch, Player)

2. **Server Route Testing**
   - Test existing routes
   - Set up integration test framework

### Phase 2: Core Functionality (Week 2)
1. **WebSocket Testing**
   - WebSocketManager unit tests
   - Integration tests for real-time features

2. **Repository Testing**
   - ServerRepository with mocked API
   - Local repository implementations

### Phase 3: Business Logic (Week 3)
1. **Use Cases Testing**
   - Match management flows
   - Game logic validation

2. **BLoC Testing**
   - State management testing
   - Event-driven flows

### Phase 4: UI and Integration (Week 4)
1. **Widget Testing**
   - Screen-level tests
   - Component tests

2. **End-to-End Testing**
   - Complete user journeys
   - Multi-client scenarios

## Testing Best Practices

### General Guidelines
1. **Test Naming**: Use descriptive names that explain the scenario
2. **Arrange-Act-Assert**: Structure tests clearly
3. **Mocking**: Mock external dependencies, test real logic
4. **Data**: Use factories for consistent test data
5. **Coverage**: Aim for 80%+ coverage on business logic

### Specific Patterns
1. **Model Testing**: Focus on serialization, validation, and business rules
2. **Repository Testing**: Mock HTTP clients, test error handling
3. **BLoC Testing**: Use `bloc_test` package for state transitions
4. **Widget Testing**: Test user interactions and state changes
5. **Integration Testing**: Test complete workflows

## Success Metrics

### Coverage Targets
- **Common Package**: 90%+ (models are critical)
- **Server Package**: 85%+ (API reliability is crucial)
- **Main App**: 75%+ (UI testing can be selective)

### Quality Gates
1. All new code must have tests
2. No breaking changes without test updates
3. Integration tests must pass before deployment
4. Performance tests for WebSocket handling

## Maintenance Strategy

### Continuous Testing
1. **CI/CD Integration**: Run tests on every commit
2. **Test Data Management**: Keep test data current with schema changes
3. **Mock Updates**: Update mocks when APIs change
4. **Documentation**: Keep test documentation current

### Regular Reviews
1. **Monthly**: Review test coverage and identify gaps
2. **Quarterly**: Update testing strategy based on new features
3. **Release**: Full regression testing before major releases

---

This plan provides a comprehensive approach to testing the Dauntless project, ensuring reliability across all components while maintaining development velocity.

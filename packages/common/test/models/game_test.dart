import 'package:common/models/game.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Game', () {
    group('constructor', () {
      test('should create a Game with required fields', () {
        // Act
        final game = ModelFactories.createGame();

        // Assert
        expect(game.id, equals('test-game-id'));
        expect(game.name, equals('Test Game'));
        expect(game.description, equals('A test game description'));
        expect(game.config, isA<Map<String, dynamic>>());
      });

      test('should create a Game with custom values', () {
        // Act
        final game = ModelFactories.createGame(
          id: 'custom-id',
          name: 'Custom Game',
          description: 'Custom description',
          config: {'customSetting': 'value'},
        );

        // Assert
        expect(game.id, equals('custom-id'));
        expect(game.name, equals('Custom Game'));
        expect(game.description, equals('Custom description'));
        expect(game.config['customSetting'], equals('value'));
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final game = ModelFactories.createGame();

        // Act
        final json = game.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('test-game-id'));
        expect(json['name'], equals('Test Game'));
        expect(json['description'], equals('A test game description'));
        expect(json['config'], isA<Map<String, dynamic>>());
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createGameJson();

        // Act
        final game = Game.fromJson(jsonData);

        // Assert
        expect(game.id, equals('test-game-id'));
        expect(game.name, equals('Test Game'));
        expect(game.description, equals('A test game description'));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalGame = ModelFactories.createGame();

        // Act
        final json = originalGame.toJson();
        final deserializedGame = Game.fromJson(json);

        // Assert
        expect(deserializedGame, equals(originalGame));
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final game1 = ModelFactories.createGame();
        final game2 = ModelFactories.createGame();

        // Assert
        expect(game1, equals(game2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final game1 = ModelFactories.createGame(id: 'id1');
        final game2 = ModelFactories.createGame(id: 'id2');

        // Assert
        expect(game1, isNot(equals(game2)));
      });
    });
  });
}

import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('PlayerSlot', () {
    group('constructor', () {
      test('should create a PlayerSlot with required fields', () {
        // Act
        final slot = ModelFactories.createPlayerSlot();

        // Assert
        expect(slot.id, equals('test-slot-id'));
        expect(slot.playerId, isNull);
        expect(slot.type, equals(PlayerType.humanLocal));
        expect(slot.playerClassId, equals('test-class-id'));
        expect(slot.name, equals('Test Slot'));
      });

      test('should create a PlayerSlot with custom values', () {
        // Act
        final slot = ModelFactories.createPlayerSlot(
          id: 'custom-slot',
          playerId: 'custom-player',
          type: PlayerType.botNetwork,
          playerClassId: 'custom-class',
          name: 'Custom Slot',
        );

        // Assert
        expect(slot.id, equals('custom-slot'));
        expect(slot.playerId, equals('custom-player'));
        expect(slot.type, equals(PlayerType.botNetwork));
        expect(slot.playerClassId, equals('custom-class'));
        expect(slot.name, equals('Custom Slot'));
      });

      test('should allow null optional fields', () {
        // Act
        final slot = PlayerSlot(
          id: 'test-id',
          type: PlayerType.humanLocal,
          playerClassId: 'class-id',
        );

        // Assert
        expect(slot.playerId, isNull);
        expect(slot.name, isNull);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final slot = ModelFactories.createPlayerSlot(
          id: 'json-slot',
          playerId: 'json-player',
          type: PlayerType.humanNetwork,
          playerClassId: 'json-class',
          name: 'JSON Slot',
        );

        // Act
        final json = slot.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['id'], equals('json-slot'));
        expect(json['playerId'], equals('json-player'));
        expect(json['type'], equals('humanNetwork'));
        expect(json['playerClassId'], equals('json-class'));
        expect(json['name'], equals('JSON Slot'));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(
          id: 'json-slot',
          playerId: 'json-player',
          type: 'humanNetwork',
          playerClassId: 'json-class',
          name: 'JSON Slot',
        );

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.id, equals('json-slot'));
        expect(slot.playerId, equals('json-player'));
        expect(slot.type, equals(PlayerType.humanNetwork));
        expect(slot.playerClassId, equals('json-class'));
        expect(slot.name, equals('JSON Slot'));
      });

      test('should handle all PlayerType values in JSON', () {
        final testCases = [
          ('humanLocal', PlayerType.humanLocal),
          ('humanNetwork', PlayerType.humanNetwork),
          ('botLocal', PlayerType.botLocal),
          ('botNetwork', PlayerType.botNetwork),
        ];

        for (final (jsonType, enumType) in testCases) {
          // Arrange
          final jsonData = ModelFactories.createPlayerSlotJson(type: jsonType);

          // Act
          final slot = PlayerSlot.fromJson(jsonData);

          // Assert
          expect(slot.type, equals(enumType), reason: 'Failed for type: $jsonType');
        }
      });

      test('should handle unknown PlayerType in JSON with default', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(type: 'unknown');

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.type, equals(PlayerType.humanLocal));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalSlot = ModelFactories.createPlayerSlot();

        // Act
        final json = originalSlot.toJson();
        final deserializedSlot = PlayerSlot.fromJson(json);

        // Assert
        expect(deserializedSlot, equals(originalSlot));
      });

      test('should handle null optional fields in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createPlayerSlotJson(
          playerId: null,
          name: null,
        );

        // Act
        final slot = PlayerSlot.fromJson(jsonData);

        // Assert
        expect(slot.playerId, isNull);
        expect(slot.name, isNull);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot();
        final slot2 = ModelFactories.createPlayerSlot();

        // Assert
        expect(slot1, equals(slot2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot(id: 'id1');
        final slot2 = ModelFactories.createPlayerSlot(id: 'id2');

        // Assert
        expect(slot1, isNot(equals(slot2)));
      });

      test('should not be equal when types differ', () {
        // Arrange
        final slot1 = ModelFactories.createPlayerSlot(type: PlayerType.humanLocal);
        final slot2 = ModelFactories.createPlayerSlot(type: PlayerType.botLocal);

        // Assert
        expect(slot1, isNot(equals(slot2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalSlot = ModelFactories.createPlayerSlot();

        // Act
        final copiedSlot = originalSlot.copyWith(
          playerId: 'new-player',
          type: PlayerType.botLocal,
          name: 'New Name',
        );

        // Assert
        expect(copiedSlot.id, equals(originalSlot.id));
        expect(copiedSlot.playerId, equals('new-player'));
        expect(copiedSlot.type, equals(PlayerType.botLocal));
        expect(copiedSlot.playerClassId, equals(originalSlot.playerClassId));
        expect(copiedSlot.name, equals('New Name'));
      });
    });
  });
}

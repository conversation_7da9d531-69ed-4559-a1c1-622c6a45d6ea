import 'package:common/models/turn.dart';
import 'package:test/test.dart';
import '../test_helpers/model_factories.dart';

void main() {
  group('Turn', () {
    group('constructor', () {
      test('should create a Turn with required fields', () {
        // Act
        final turn = ModelFactories.createTurn();

        // Assert
        expect(turn.playerId, equals('test-player-id'));
        expect(turn.move, equals('test-move-data'));
        expect(turn.timestamp, isA<String>());
        expect(turn.turnNumber, equals(1));
        expect(turn.metadata, isEmpty);
      });

      test('should create a Turn with custom values', () {
        // Act
        final turn = ModelFactories.createTurn(
          playerId: 'custom-player',
          move: 'custom-move',
          timestamp: '2023-01-01T00:00:00Z',
          turnNumber: 5,
          metadata: {'duration': 30},
        );

        // Assert
        expect(turn.playerId, equals('custom-player'));
        expect(turn.move, equals('custom-move'));
        expect(turn.timestamp, equals('2023-01-01T00:00:00Z'));
        expect(turn.turnNumber, equals(5));
        expect(turn.metadata['duration'], equals(30));
      });

      test('should use default metadata when not specified', () {
        // Act
        final turn = Turn(
          playerId: 'test-player',
          move: 'test-move',
          timestamp: '2023-01-01T00:00:00Z',
          turnNumber: 1,
        );

        // Assert
        expect(turn.metadata, isEmpty);
      });
    });

    group('JSON serialization', () {
      test('should serialize to JSON correctly', () {
        // Arrange
        final turn = ModelFactories.createTurn(
          playerId: 'json-player',
          move: 'json-move',
          timestamp: '2023-01-01T12:00:00Z',
          turnNumber: 3,
          metadata: {'score': 100},
        );

        // Act
        final json = turn.toJson();

        // Assert
        expect(json, isA<Map<String, dynamic>>());
        expect(json['playerId'], equals('json-player'));
        expect(json['move'], equals('json-move'));
        expect(json['timestamp'], equals('2023-01-01T12:00:00Z'));
        expect(json['turnNumber'], equals(3));
        expect(json['metadata'], isA<Map<String, dynamic>>());
        expect(json['metadata']['score'], equals(100));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final jsonData = ModelFactories.createTurnJson(
          playerId: 'json-player',
          move: 'json-move',
          timestamp: '2023-01-01T12:00:00Z',
          turnNumber: 3,
          metadata: {'score': 100},
        );

        // Act
        final turn = Turn.fromJson(jsonData);

        // Assert
        expect(turn.playerId, equals('json-player'));
        expect(turn.move, equals('json-move'));
        expect(turn.timestamp, equals('2023-01-01T12:00:00Z'));
        expect(turn.turnNumber, equals(3));
        expect(turn.metadata['score'], equals(100));
      });

      test('should handle round-trip JSON conversion', () {
        // Arrange
        final originalTurn = ModelFactories.createTurn(
          metadata: {'complex': {'nested': 'value'}, 'array': [1, 2, 3]},
        );

        // Act
        final json = originalTurn.toJson();
        final deserializedTurn = Turn.fromJson(json);

        // Assert
        expect(deserializedTurn, equals(originalTurn));
      });

      test('should handle empty metadata in JSON', () {
        // Arrange
        final jsonData = ModelFactories.createTurnJson(metadata: {});

        // Act
        final turn = Turn.fromJson(jsonData);

        // Assert
        expect(turn.metadata, isEmpty);
      });
    });

    group('equality', () {
      test('should be equal when all properties are the same', () {
        // Arrange
        const fixedTimestamp = '2023-01-01T00:00:00Z';
        final turn1 = ModelFactories.createTurn(timestamp: fixedTimestamp);
        final turn2 = ModelFactories.createTurn(timestamp: fixedTimestamp);

        // Assert
        expect(turn1, equals(turn2));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final turn1 = ModelFactories.createTurn(turnNumber: 1);
        final turn2 = ModelFactories.createTurn(turnNumber: 2);

        // Assert
        expect(turn1, isNot(equals(turn2)));
      });

      test('should not be equal when move data differs', () {
        // Arrange
        final turn1 = ModelFactories.createTurn(move: 'move1');
        final turn2 = ModelFactories.createTurn(move: 'move2');

        // Assert
        expect(turn1, isNot(equals(turn2)));
      });
    });

    group('copyWith', () {
      test('should create copy with modified properties', () {
        // Arrange
        final originalTurn = ModelFactories.createTurn();

        // Act
        final copiedTurn = originalTurn.copyWith(
          move: 'new-move',
          turnNumber: 99,
          metadata: {'new': 'metadata'},
        );

        // Assert
        expect(copiedTurn.playerId, equals(originalTurn.playerId));
        expect(copiedTurn.move, equals('new-move'));
        expect(copiedTurn.timestamp, equals(originalTurn.timestamp));
        expect(copiedTurn.turnNumber, equals(99));
        expect(copiedTurn.metadata['new'], equals('metadata'));
      });
    });

    group('edge cases', () {
      test('should handle very long move data', () {
        // Arrange
        final longMove = 'a' * 10000;

        // Act
        final turn = ModelFactories.createTurn(move: longMove);

        // Assert
        expect(turn.move, equals(longMove));
      });

      test('should handle complex move data as JSON string', () {
        // Arrange
        const complexMove = '{"action":"move","coordinates":[1,2,3],"options":{"fast":true}}';

        // Act
        final turn = ModelFactories.createTurn(move: complexMove);

        // Assert
        expect(turn.move, equals(complexMove));
      });

      test('should handle zero and negative turn numbers', () {
        // Act
        final turn1 = ModelFactories.createTurn(turnNumber: 0);
        final turn2 = ModelFactories.createTurn(turnNumber: -1);

        // Assert
        expect(turn1.turnNumber, equals(0));
        expect(turn2.turnNumber, equals(-1));
      });
    });
  });
}

import 'package:common/models/game.dart';
import 'package:common/models/game_match.dart';
import 'package:common/models/match_status.dart';
import 'package:common/models/player.dart';
import 'package:common/models/player_slot.dart';
import 'package:common/models/player_type.dart';
import 'package:common/models/turn.dart';

/// Factory class for creating test model instances
class ModelFactories {
  /// Create a test Game instance
  static Game createGame({
    String? id,
    String? name,
    String? description,
    Map<String, dynamic>? config,
  }) {
    return Game(
      id: id ?? 'test-game-id',
      name: name ?? 'Test Game',
      description: description ?? 'A test game description',
      config: config ?? {'maxPlayers': 4, 'turnTimeLimit': 300},
    );
  }

  /// Create a test GameMatch instance
  static GameMatch createGameMatch({
    String? id,
    String? gameTypeId,
    String? creatorId,
    String? gameName,
    int? createdAt,
    int? updatedAt,
    MatchStatus? status,
    bool? isOpenForJoining,
    List<PlayerSlot>? playerSlots,
    List<Player>? players,
    List<Turn>? turns,
    int? currentTurn,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return GameMatch(
      id: id ?? 'test-match-id',
      gameTypeId: gameTypeId ?? 'test-game-type',
      creatorId: creatorId ?? 'test-creator-id',
      gameName: gameName,
      createdAt: createdAt ?? now,
      updatedAt: updatedAt ?? now,
      status: status ?? MatchStatus.open,
      isOpenForJoining: isOpenForJoining ?? true,
      playerSlots: playerSlots ?? [],
      players: players ?? [],
      turns: turns ?? [],
      currentTurn: currentTurn ?? 0,
    );
  }

  /// Create a test Player instance
  static Player createPlayer({
    String? id,
    String? name,
    PlayerType? type,
    Map<String, dynamic>? metadata,
  }) {
    return Player(
      id: id ?? 'test-player-id',
      name: name ?? 'Test Player',
      type: type ?? PlayerType.humanNetwork,
      metadata: metadata ?? {},
    );
  }

  /// Create a test PlayerSlot instance
  static PlayerSlot createPlayerSlot({
    String? id,
    String? playerId,
    PlayerType? type,
    String? playerClassId,
    String? name,
  }) {
    return PlayerSlot(
      id: id ?? 'test-slot-id',
      playerId: playerId,
      type: type ?? PlayerType.humanLocal,
      playerClassId: playerClassId ?? 'test-class-id',
      name: name ?? 'Test Slot',
    );
  }

  /// Create a test Turn instance
  static Turn createTurn({
    String? playerId,
    String? move,
    String? timestamp,
    int? turnNumber,
    Map<String, dynamic>? metadata,
  }) {
    return Turn(
      playerId: playerId ?? 'test-player-id',
      move: move ?? 'test-move-data',
      timestamp: timestamp ?? DateTime.now().toIso8601String(),
      turnNumber: turnNumber ?? 1,
      metadata: metadata ?? {},
    );
  }

  /// Create a complete GameMatch with players and turns for testing
  static GameMatch createCompleteGameMatch({
    int playerCount = 2,
    int turnCount = 3,
  }) {
    final players = List.generate(
      playerCount,
      (index) => createPlayer(
        id: 'player-$index',
        name: 'Player $index',
      ),
    );

    final playerSlots = List.generate(
      playerCount,
      (index) => createPlayerSlot(
        id: 'slot-$index',
        playerId: 'player-$index',
        name: 'Player $index Slot',
      ),
    );

    final turns = List.generate(
      turnCount,
      (index) => createTurn(
        playerId: 'player-${index % playerCount}',
        turnNumber: index + 1,
        move: 'move-${index + 1}',
      ),
    );

    return createGameMatch(
      players: players,
      playerSlots: playerSlots,
      turns: turns,
      currentTurn: turnCount,
      status: turnCount > 0 ? MatchStatus.active : MatchStatus.open,
    );
  }

  /// Create JSON data for testing serialization
  static Map<String, dynamic> createGameJson({
    String? id,
    String? name,
    String? description,
    Map<String, dynamic>? config,
  }) {
    return {
      'id': id ?? 'test-game-id',
      'name': name ?? 'Test Game',
      'description': description ?? 'A test game description',
      'config': config ?? {'maxPlayers': 4, 'turnTimeLimit': 300},
    };
  }

  /// Create JSON data for GameMatch testing
  static Map<String, dynamic> createGameMatchJson({
    String? id,
    String? gameTypeId,
    String? creatorId,
    String? gameName,
    int? createdAt,
    int? updatedAt,
    String? status,
    bool? isOpenForJoining,
    List<Map<String, dynamic>>? playerSlots,
    List<Map<String, dynamic>>? players,
    List<Map<String, dynamic>>? turns,
    int? currentTurn,
  }) {
    final now = DateTime.now().millisecondsSinceEpoch;
    return {
      'id': id ?? 'test-match-id',
      'gameTypeId': gameTypeId ?? 'test-game-type',
      'creatorId': creatorId ?? 'test-creator-id',
      if (gameName != null) 'gameName': gameName,
      'created_at': createdAt ?? now,
      'updated_at': updatedAt ?? now,
      'status': status ?? 'open',
      'is_open_for_joining': isOpenForJoining ?? true,
      'playerSlots': playerSlots ?? [],
      'players': players ?? [],
      'turns': turns ?? [],
      'currentTurn': currentTurn ?? 0,
    };
  }

  /// Create JSON data for Player testing
  static Map<String, dynamic> createPlayerJson({
    String? id,
    String? name,
    String? type,
    Map<String, dynamic>? metadata,
  }) {
    return {
      'id': id ?? 'test-player-id',
      if (name != null) 'name': name,
      'type': type ?? 'humanNetwork',
      'metadata': metadata ?? {},
    };
  }

  /// Create JSON data for PlayerSlot testing
  static Map<String, dynamic> createPlayerSlotJson({
    String? id,
    String? playerId,
    String? type,
    String? playerClassId,
    String? name,
  }) {
    return {
      'id': id ?? 'test-slot-id',
      if (playerId != null) 'playerId': playerId,
      'type': type ?? 'humanLocal',
      'playerClassId': playerClassId ?? 'test-class-id',
      if (name != null) 'name': name,
    };
  }

  /// Create JSON data for Turn testing
  static Map<String, dynamic> createTurnJson({
    String? playerId,
    String? move,
    String? timestamp,
    int? turnNumber,
    Map<String, dynamic>? metadata,
  }) {
    return {
      'playerId': playerId ?? 'test-player-id',
      'move': move ?? 'test-move-data',
      'timestamp': timestamp ?? DateTime.now().toIso8601String(),
      'turnNumber': turnNumber ?? 1,
      'metadata': metadata ?? {},
    };
  }
}

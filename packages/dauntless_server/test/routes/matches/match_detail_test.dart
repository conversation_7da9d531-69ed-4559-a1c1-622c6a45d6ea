import 'dart:convert';
import 'dart:io';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../routes/matches/[id]/index.dart' as route;
import '../../test_helpers/mock_controllers.dart';

class _MockRequestContext extends Mock implements RequestContext {}
class _MockRequest extends Mock implements Request {}

void main() {
  group('/matches/{id}', () {
    late MockGameMatchController mockController;
    late _MockRequestContext mockContext;
    late _MockRequest mockRequest;

    setUp(() {
      mockController = MockGameMatchController();
      mockContext = _MockRequestContext();
      mockRequest = _MockRequest();

      when(() => mockContext.read<GameMatchController>()).thenReturn(mockController);
      when(() => mockContext.request).thenReturn(mockRequest);
    });

    group('GET', () {
      setUp(() {
        when(() => mockRequest.method).thenReturn(HttpMethod.get);
      });

      test('should return match when found', () async {
        // Arrange
        const matchId = 'test-match-123';
        final matchData = TestMatchData.createMatch(
          id: matchId,
          gameName: 'Test Match',
          status: 'active',
        );
        when(() => mockController.getMatch(matchId)).thenAnswer((_) async => matchData);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['id'], equals(matchId));
        expect(responseData['gameName'], equals('Test Match'));
        expect(responseData['status'], equals('active'));
        
        verify(() => mockController.getMatch(matchId)).called(1);
      });

      test('should return 404 when match not found', () async {
        // Arrange
        const matchId = 'non-existent-match';
        when(() => mockController.getMatch(matchId)).thenAnswer((_) async => null);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.notFound));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], equals('Match not found'));
      });

      test('should return 404 when match is empty', () async {
        // Arrange
        const matchId = 'empty-match';
        when(() => mockController.getMatch(matchId)).thenAnswer((_) async => {});

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.notFound));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], equals('Match not found'));
      });

      test('should handle controller errors', () async {
        // Arrange
        const matchId = 'error-match';
        when(() => mockController.getMatch(matchId)).thenThrow(Exception('Database error'));

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to retrieve game_match'));
        expect(responseData['error'], contains('Database error'));
      });
    });

    group('PUT', () {
      setUp(() {
        when(() => mockRequest.method).thenReturn(HttpMethod.put);
      });

      test('should update match status successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        final updateData = {'status': 'active'};
        final updatedMatch = TestMatchData.createMatch(
          id: matchId,
          status: 'active',
        );

        when(() => mockRequest.body()).thenAnswer((_) async => jsonEncode(updateData));
        when(() => mockController.updateMatchStatus(matchId, updateData))
            .thenAnswer((_) async => updatedMatch);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['id'], equals(matchId));
        expect(responseData['status'], equals('active'));
        
        verify(() => mockController.updateMatchStatus(matchId, updateData)).called(1);
      });

      test('should return 404 when match not found for update', () async {
        // Arrange
        const matchId = 'non-existent-match';
        final updateData = {'status': 'active'};

        when(() => mockRequest.body()).thenAnswer((_) async => jsonEncode(updateData));
        when(() => mockController.updateMatchStatus(matchId, updateData))
            .thenAnswer((_) async => null);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.notFound));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], equals('Match not found'));
      });

      test('should handle invalid JSON in update request', () async {
        // Arrange
        const matchId = 'test-match-123';
        when(() => mockRequest.body()).thenAnswer((_) async => 'invalid json');

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to update game_match status'));
      });
    });

    group('DELETE', () {
      setUp(() {
        when(() => mockRequest.method).thenReturn(HttpMethod.delete);
      });

      test('should delete match successfully', () async {
        // Arrange
        const matchId = 'test-match-123';
        when(() => mockController.deleteMatch(matchId)).thenAnswer((_) async => true);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['success'], isTrue);
        expect(responseData['message'], equals('Match successfully deleted'));
        expect(responseData['id'], equals(matchId));
        
        verify(() => mockController.deleteMatch(matchId)).called(1);
      });

      test('should return 404 when match not found for deletion', () async {
        // Arrange
        const matchId = 'non-existent-match';
        when(() => mockController.deleteMatch(matchId)).thenAnswer((_) async => false);

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.notFound));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], equals('Match not found or could not be deleted'));
      });

      test('should handle controller errors during deletion', () async {
        // Arrange
        const matchId = 'error-match';
        when(() => mockController.deleteMatch(matchId)).thenThrow(Exception('Delete failed'));

        // Act
        final response = await route.onRequest(mockContext, matchId);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to delete game_match'));
        expect(responseData['error'], contains('Delete failed'));
      });
    });

    group('unsupported methods', () {
      test('should return 405 for POST method', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.post);

        // Act
        final response = await route.onRequest(mockContext, 'test-id');

        // Assert
        expect(response.statusCode, equals(405));
        expect(await response.body(), equals('Method not allowed'));
      });

      test('should return 405 for PATCH method', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.patch);

        // Act
        final response = await route.onRequest(mockContext, 'test-id');

        // Assert
        expect(response.statusCode, equals(405));
        expect(await response.body(), equals('Method not allowed'));
      });
    });
  });
}

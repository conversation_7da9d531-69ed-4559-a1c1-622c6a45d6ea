import 'dart:convert';
import 'dart:io';

import 'package:dart_frog/dart_frog.dart';
import 'package:dauntless_server/controllers/game_match_controller.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../../routes/matches/index.dart' as route;
import '../../test_helpers/mock_controllers.dart';

class _MockRequestContext extends Mock implements RequestContext {}
class _MockRequest extends Mock implements Request {}
class _MockUri extends Mock implements Uri {}

void main() {
  group('/matches', () {
    late MockGameMatchController mockController;
    late _MockRequestContext mockContext;
    late _MockRequest mockRequest;
    late _MockUri mockUri;

    setUp(() {
      mockController = MockGameMatchController();
      mockContext = _MockRequestContext();
      mockRequest = _MockRequest();
      mockUri = _MockUri();

      // Setup basic mocks
      when(() => mockContext.read<GameMatchController>()).thenReturn(mockController);
      when(() => mockContext.request).thenReturn(mockRequest);
      when(() => mockRequest.uri).thenReturn(mockUri);
    });

    group('GET', () {
      setUp(() {
        when(() => mockRequest.method).thenReturn(HttpMethod.get);
      });

      test('should return open matches by default', () async {
        // Arrange
        final openMatches = TestMatchData.createOpenMatches(count: 2);
        when(() => mockUri.queryParameters).thenReturn({});
        when(() => mockController.getOpenMatches()).thenAnswer((_) async => openMatches);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as List;
        expect(responseData.length, equals(2));
        expect(responseData[0]['id'], equals('open-match-0'));
        expect(responseData[0]['status'], equals('open'));
        
        verify(() => mockController.getOpenMatches()).called(1);
        verifyNever(() => mockController.getMatches());
      });

      test('should return all matches when showAll=true', () async {
        // Arrange
        final allMatches = TestMatchData.createAllMatches(count: 4);
        when(() => mockUri.queryParameters).thenReturn({'showAll': 'true'});
        when(() => mockController.getMatches()).thenAnswer((_) async => allMatches);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as List;
        expect(responseData.length, equals(4));
        
        verify(() => mockController.getMatches()).called(1);
        verifyNever(() => mockController.getOpenMatches());
      });

      test('should return open matches when showAll=false', () async {
        // Arrange
        final openMatches = TestMatchData.createOpenMatches(count: 1);
        when(() => mockUri.queryParameters).thenReturn({'showAll': 'false'});
        when(() => mockController.getOpenMatches()).thenAnswer((_) async => openMatches);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.ok));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as List;
        expect(responseData.length, equals(1));
        
        verify(() => mockController.getOpenMatches()).called(1);
      });

      test('should handle controller errors gracefully', () async {
        // Arrange
        when(() => mockUri.queryParameters).thenReturn({});
        when(() => mockController.getOpenMatches()).thenThrow(Exception('Database error'));

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to retrieve matches'));
        expect(responseData['error'], contains('Database error'));
      });
    });

    group('POST', () {
      setUp(() {
        when(() => mockRequest.method).thenReturn(HttpMethod.post);
      });

      test('should create a new match successfully', () async {
        // Arrange
        final requestData = TestMatchData.createMatchRequest(
          gameTypeId: 'liberator',
          creatorId: 'player-123',
          gameName: 'Test Match',
        );
        final createdMatch = TestMatchData.createMatch(
          id: 'new-match-id',
          gameTypeId: 'liberator',
          creatorId: 'player-123',
          gameName: 'Test Match',
        );

        when(() => mockRequest.body()).thenAnswer((_) async => jsonEncode(requestData));
        when(() => mockController.createMatch(any())).thenAnswer((_) async => createdMatch);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.created));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['id'], equals('new-match-id'));
        expect(responseData['gameTypeId'], equals('liberator'));
        expect(responseData['gameName'], equals('Test Match'));
        
        verify(() => mockController.createMatch(any())).called(1);
      });

      test('should handle invalid JSON in request body', () async {
        // Arrange
        when(() => mockRequest.body()).thenAnswer((_) async => 'invalid json');

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to create game_match'));
      });

      test('should handle controller errors during creation', () async {
        // Arrange
        final requestData = TestMatchData.createMatchRequest();
        when(() => mockRequest.body()).thenAnswer((_) async => jsonEncode(requestData));
        when(() => mockController.createMatch(any())).thenThrow(Exception('Creation failed'));

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(HttpStatus.internalServerError));
        
        final responseBody = await response.body();
        final responseData = jsonDecode(responseBody) as Map<String, dynamic>;
        expect(responseData['error'], contains('Failed to create game_match'));
        expect(responseData['error'], contains('Creation failed'));
      });
    });

    group('unsupported methods', () {
      test('should return 405 for PUT method', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.put);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(405));
        expect(await response.body(), equals('Method not allowed'));
      });

      test('should return 405 for DELETE method', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.delete);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(405));
        expect(await response.body(), equals('Method not allowed'));
      });

      test('should return 405 for PATCH method', () async {
        // Arrange
        when(() => mockRequest.method).thenReturn(HttpMethod.patch);

        // Act
        final response = await route.onRequest(mockContext);

        // Assert
        expect(response.statusCode, equals(405));
        expect(await response.body(), equals('Method not allowed'));
      });
    });
  });
}

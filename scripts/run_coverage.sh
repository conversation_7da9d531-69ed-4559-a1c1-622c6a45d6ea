#!/bin/bash

# Test Coverage Runner for Dauntless Project
# Usage: ./scripts/run_coverage.sh [common|server|all]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to run coverage for a package
run_package_coverage() {
    local package_name=$1
    local package_path=$2
    
    print_status "Running coverage for $package_name..."
    
    cd "$package_path"
    
    # Run tests with coverage
    dart test --coverage=coverage
    
    # Format coverage data
    dart pub global run coverage:format_coverage \
        --lcov \
        --in=coverage \
        --out=coverage/lcov.info \
        --packages=.dart_tool/package_config.json \
        --report-on=lib
    
    # Generate coverage summary
    if [ -f "coverage/lcov.info" ]; then
        print_status "Generating coverage summary for $package_name..."
        
        python3 -c "
import re
with open('coverage/lcov.info', 'r') as f:
    content = f.read()

lf_matches = re.findall(r'^LF:(\d+)', content, re.MULTILINE)
lh_matches = re.findall(r'^LH:(\d+)', content, re.MULTILINE)

total_lines = sum(int(x) for x in lf_matches)
hit_lines = sum(int(x) for x in lh_matches)

if total_lines > 0:
    coverage = (hit_lines / total_lines) * 100
    status = '✅' if coverage > 80 else '⚠️' if coverage > 50 else '❌'
    print(f'{status} $package_name Coverage: {hit_lines}/{total_lines} lines ({coverage:.1f}%)')
else:
    print('❌ No coverage data found for $package_name')
"
    else
        print_error "Coverage file not found for $package_name"
    fi
    
    cd - > /dev/null
}

# Function to run coverage for common package
run_common_coverage() {
    print_status "=== COMMON PACKAGE COVERAGE ==="
    run_package_coverage "Common" "packages/common"
}

# Function to run coverage for server package
run_server_coverage() {
    print_status "=== SERVER PACKAGE COVERAGE ==="
    run_package_coverage "Server" "packages/dauntless_server"
}

# Function to run coverage for all packages
run_all_coverage() {
    print_status "=== RUNNING COVERAGE FOR ALL PACKAGES ==="
    run_common_coverage
    echo
    run_server_coverage
    echo
    print_success "Coverage analysis complete!"
    print_status "Check individual package coverage/ directories for detailed reports"
}

# Main script logic
case "${1:-all}" in
    "common")
        run_common_coverage
        ;;
    "server")
        run_server_coverage
        ;;
    "all")
        run_all_coverage
        ;;
    *)
        print_error "Usage: $0 [common|server|all]"
        exit 1
        ;;
esac

print_success "Coverage run completed!"
